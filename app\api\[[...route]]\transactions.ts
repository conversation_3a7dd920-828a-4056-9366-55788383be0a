import { Hono } from "hono";
import { db } from "@/db/drizzle";
import { parse, subDays } from "date-fns";
import { transactions, accounts, categories, mediaFiles } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { zValidator } from "@hono/zod-validator";
import { and, eq, inArray, desc, lte, gte, sql } from "drizzle-orm";
import { insertTransactionSchema } from "@/db/schema";
import { z } from "zod";
import { convertAmountFormMiliunits, getServerUserId } from "@/lib/utils";
import { detailsTransactions, itemTransactions, items } from "@/db/schema";
import { DetailsTransactionsType, ItemTransactionsType } from "@/db/schema";
import { getS3Storage } from "@/lib/s3-storage";

const app = new Hono()
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        from: z.string().optional(),
        to: z.string().optional(),
        accountId: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);

        const { from, to, accountId } = c.req.valid("query");

        const defaultTo = new Date();
        const defaultFrom = subDays(defaultTo, 30);
        const startDate = from
          ? parse(from, "yyyy-MM-dd", new Date())
          : defaultFrom;
        const endDate = to ? parse(to, "yyyy-MM-dd", new Date()) : defaultTo;

        const transactionData = await db
          .select({
            id: transactions.id,
            amount: transactions.amount,
            account: accounts.name,
            category: categories.name,
            payee: transactions.payee,
            notes: transactions.notes,
            date: transactions.date,
            projectId: transactions.projectId,
            accountId: transactions.accountId,
            categoryId: transactions.categoryId,
          })
          .from(transactions)
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .leftJoin(categories, eq(transactions.categoryId, categories.id))
          .where(
            and(
              accountId ? eq(transactions.accountId, accountId) : undefined,
              eq(accounts.userId, userId),
              gte(transactions.date, startDate),
              lte(transactions.date, endDate),
            ),
          )
          .orderBy(desc(transactions.date));

        let details: DetailsTransactionsType[] = [];
        let itemDetails: (ItemTransactionsType & { itemName: string; itemDescription?: string | null })[] = [];
        
        if (transactionData.length > 0) {
          const transactionsId = transactionData.map((t) => t.id);
          
          // Get old-style detail transactions
          details = await db
            .select({
              id: detailsTransactions.id,
              name: detailsTransactions.name,
              quantity: detailsTransactions.quantity,
              unitPrice: detailsTransactions.unitPrice,
              amount: detailsTransactions.amount,
              transactionId: detailsTransactions.transactionId,
              CategoryId: detailsTransactions.categoryId,
            })
            .from(detailsTransactions)
            .leftJoin(
              categories,
              eq(detailsTransactions.categoryId, categories.id),
            )
            .where(inArray(detailsTransactions.transactionId, transactionsId));

          // Get new-style item transactions with item information
          itemDetails = await db
            .select({
              id: itemTransactions.id,
              itemId: itemTransactions.itemId,
              transactionId: itemTransactions.transactionId,
              quantity: itemTransactions.quantity,
              unitPrice: itemTransactions.unitPrice,
              amount: itemTransactions.amount,
              categoryId: itemTransactions.categoryId,
              projectId: itemTransactions.projectId,
              createdAt: itemTransactions.createdAt,
              updatedAt: itemTransactions.updatedAt,
              itemName: items.name,
              itemDescription: items.description,
            })
            .from(itemTransactions)
            .innerJoin(items, eq(itemTransactions.itemId, items.id))
            .where(inArray(itemTransactions.transactionId, transactionsId));
        }

        details = details.map((detail) => ({
          ...detail,
          amount: convertAmountFormMiliunits(detail.amount ? detail.amount : 0),
          unitPrice: convertAmountFormMiliunits(
            detail.unitPrice ? detail.unitPrice : 0,
          ),
        }));

        itemDetails = itemDetails.map((detail) => ({
          ...detail,
          amount: convertAmountFormMiliunits(detail.amount ? detail.amount : 0),
          unitPrice: convertAmountFormMiliunits(
            detail.unitPrice ? detail.unitPrice : 0,
          ),
        }));

        const data = transactionData.map((transaction) => ({
          ...transaction,
          amount: convertAmountFormMiliunits(transaction.amount),
          detailsTransactions: details.filter(
            (d) => d.transactionId === transaction.id,
          ),
          itemTransactions: itemDetails.filter(
            (d) => d.transactionId === transaction.id,
          ),
        }));

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .get("/:id", zValidator("param", z.object({ id: z.string() })), async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);
      const { id } = c.req.valid("param");

      const transactionData = await db
        .select({
          id: transactions.id,
          amount: transactions.amount,
          account: accounts.name,
          category: categories.name,
          payee: transactions.payee,
          notes: transactions.notes,
          date: transactions.date,
          projectId: transactions.projectId,
          accountId: transactions.accountId,
          categoryId: transactions.categoryId,
        })
        .from(transactions)
        .innerJoin(accounts, eq(transactions.accountId, accounts.id))
        .leftJoin(categories, eq(transactions.categoryId, categories.id))
        .where(and(eq(transactions.id, id!), eq(accounts.userId, userId)));

      let details: DetailsTransactionsType[] = [];
      let itemDetails: (ItemTransactionsType & { itemName: string; itemDescription?: string | null })[] = [];
      
      if (transactionData.length > 0) {
        const transactionsId = transactionData.map((t) => t.id);
        
        // Get old-style detail transactions
        details = await db
          .select({
            id: detailsTransactions.id,
            name: detailsTransactions.name,
            quantity: detailsTransactions.quantity,
            unitPrice: detailsTransactions.unitPrice,
            amount: detailsTransactions.amount,
            transactionId: detailsTransactions.transactionId,
            categoryId: detailsTransactions.categoryId,
            projectId: detailsTransactions.projectId,
          })
          .from(detailsTransactions)
          .leftJoin(
            categories,
            eq(detailsTransactions.categoryId, categories.id),
          )
          .where(inArray(detailsTransactions.transactionId, transactionsId));

        // Get new-style item transactions with item information
        itemDetails = await db
          .select({
            id: itemTransactions.id,
            itemId: itemTransactions.itemId,
            transactionId: itemTransactions.transactionId,
            quantity: itemTransactions.quantity,
            unitPrice: itemTransactions.unitPrice,
            amount: itemTransactions.amount,
            categoryId: itemTransactions.categoryId,
            projectId: itemTransactions.projectId,
            createdAt: itemTransactions.createdAt,
            updatedAt: itemTransactions.updatedAt,
            itemName: items.name,
            itemDescription: items.description,
          })
          .from(itemTransactions)
          .innerJoin(items, eq(itemTransactions.itemId, items.id))
          .where(inArray(itemTransactions.transactionId, transactionsId));
      }

      details = details.map((detail) => ({
        ...detail,
        amount: convertAmountFormMiliunits(detail.amount),
        unitPrice: convertAmountFormMiliunits(
          !detail.unitPrice ? 0 : detail.unitPrice,
        ),
      }));

      itemDetails = itemDetails.map((detail) => ({
        ...detail,
        amount: convertAmountFormMiliunits(detail.amount),
        unitPrice: convertAmountFormMiliunits(
          !detail.unitPrice ? 0 : detail.unitPrice,
        ),
      }));

      const data = transactionData.map((transaction) => ({
        ...transaction,
        amount: convertAmountFormMiliunits(transaction.amount),
        detailsTransactions: details.filter(
          (d) => d.transactionId === transaction.id,
        ),
        itemTransactions: itemDetails.filter(
          (d) => d.transactionId === transaction.id,
        ),
      }));

      if (!data) {
        return c.json(
          {
            error: "Not found",
          },
          404,
        );
      }

      return c.json({ data });
    } catch (error) {
      return c.json({ error: "Unauthorized" }, 401);
    }
  })
  .post(
    "/",
    zValidator(
      "json",
      insertTransactionSchema.omit({
        id: true,
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const [data] = await db
          .insert(transactions)
          .values({
            id: createId(),
            ...values,
          })
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/with-files",
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const formData = await c.req.formData();
        
        // Extract transaction data
        const transactionData = JSON.parse(formData.get("data") as string);

        // Convert date string back to Date object if present
        if (transactionData.date) {
          transactionData.date = new Date(transactionData.date);
        }

        // Create transaction first
        const [transaction] = await db
          .insert(transactions)
          .values({
            id: createId(),
            ...transactionData,
          })
          .returning();

        // Handle file uploads if any
        const uploadedFiles: any[] = [];
        const s3Storage = getS3Storage();

        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file-') && value instanceof File) {
            try {
              // Upload to S3
              const uploadResult = await s3Storage.uploadFile(
                value,
                value.name,
                {
                  userId,
                  category: 'receipts',
                  filename: `${transaction.id}-${value.name}`,
                  contentType: value.type,
                }
              );

              // Save to database
              const [mediaFile] = await db
                .insert(mediaFiles)
                .values({
                  id: createId(),
                  userId,
                  fileName: uploadResult.key.split('/').pop() || value.name,
                  originalFileName: value.name,
                  mimeType: value.type,
                  fileSize: value.size,
                  s3Key: uploadResult.key,
                  s3Url: uploadResult.url,
                  category: 'receipts',
                  entityType: 'transaction',
                  entityId: transaction.id,
                })
                .returning();

              uploadedFiles.push(mediaFile);
            } catch (fileError) {
              console.error(`Error uploading file ${value.name}:`, fileError);
            }
          }
        }

        return c.json({ 
          data: transaction, 
          uploadedFiles: uploadedFiles.length,
          message: `Transaction created with ${uploadedFiles.length} file(s)`
        });
      } catch (error) {
        console.error('Transaction creation error:', error);
        return c.json({ error: "Failed to create transaction" }, 500);
      }
    },
  )
  .post(
    "/bulk-delete",
    zValidator(
      "json",
      z.object({
        ids: z.array(z.string()),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const transactionsToDelete = db.$with("transactions_to_delete").as(
          db
            .select({
              id: transactions.id,
            })
            .from(transactions)
            .innerJoin(accounts, eq(transactions.accountId, accounts.id))
            .where(
              and(
                inArray(transactions.id, values.ids),
                eq(accounts.userId, userId),
              ),
            ),
        );

        const data = await db
          .with(transactionsToDelete)
          .delete(transactions)
          .where(
            and(
              inArray(
                transactions.id,
                sql`(select id from ${transactionsToDelete})`,
              ),
            ),
          )
          .returning({
            id: transactions.id,
          });

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/bulk-create",

    zValidator(
      "json",
      z.array(
        insertTransactionSchema.omit({
          id: true,
        }),
      ),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const data = await db
          .insert(transactions)
          .values(
            values.map((value) => ({
              id: createId(),
              ...value,
            })),
          )
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id",

    zValidator(
      "param",
      z.object({
        id: z.string().optional(),
      }),
    ),
    zValidator(
      "json",
      insertTransactionSchema.omit({
        id: true,
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        if (!id) {
          return c.json({ error: "Id not found" }, 400);
        }

        const transactionsToUpdate = db.$with("transactions_to_update").as(
          db
            .select({ id: transactions.id })
            .from(transactions)
            .innerJoin(accounts, eq(transactions.accountId, accounts.id))
            .where(and(eq(transactions.id, id), eq(accounts.userId, userId))),
        );
        const [data] = await db
          .with(transactionsToUpdate)
          .update(transactions)
          .set(values)
          .where(
            inArray(
              transactions.id,
              sql`(select id from  ${transactionsToUpdate})`,
            ),
          )
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id/with-files",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const formData = await c.req.formData();
        
        // Extract transaction data
        const transactionData = JSON.parse(formData.get("data") as string);

        // Convert date string back to Date object if present
        if (transactionData.date) {
          transactionData.date = new Date(transactionData.date);
        }

        // Verify user owns the transaction
        const transactionsToUpdate = db.$with("transactions_to_update").as(
          db
            .select({ id: transactions.id })
            .from(transactions)
            .innerJoin(accounts, eq(transactions.accountId, accounts.id))
            .where(and(eq(transactions.id, id), eq(accounts.userId, userId))),
        );

        // Update transaction
        const [transaction] = await db
          .with(transactionsToUpdate)
          .update(transactions)
          .set(transactionData)
          .where(
            inArray(
              transactions.id,
              sql`(select id from ${transactionsToUpdate})`,
            ),
          )
          .returning();

        if (!transaction) {
          return c.json({ error: "Transaction not found" }, 404);
        }

        // Handle file uploads if any
        const uploadedFiles: any[] = [];
        const s3Storage = getS3Storage();

        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file-') && value instanceof File) {
            try {
              // Upload to S3
              const uploadResult = await s3Storage.uploadFile(
                value,
                value.name,
                {
                  userId,
                  category: 'receipts',
                  filename: `${transaction.id}-${value.name}`,
                  contentType: value.type,
                }
              );

              // Save to database
              const [mediaFile] = await db
                .insert(mediaFiles)
                .values({
                  id: createId(),
                  userId,
                  fileName: uploadResult.key.split('/').pop() || value.name,
                  originalFileName: value.name,
                  mimeType: value.type,
                  fileSize: value.size,
                  s3Key: uploadResult.key,
                  s3Url: uploadResult.url,
                  category: 'receipts',
                  entityType: 'transaction',
                  entityId: transaction.id,
                })
                .returning();

              uploadedFiles.push(mediaFile);
            } catch (fileError) {
              console.error(`Error uploading file ${value.name}:`, fileError);
            }
          }
        }

        return c.json({ 
          data: transaction, 
          uploadedFiles: uploadedFiles.length,
          message: `Transaction updated with ${uploadedFiles.length} new file(s)`
        });
      } catch (error) {
        console.error('Transaction update error:', error);
        return c.json({ error: "Failed to update transaction" }, 500);
      }
    },
  )
  .delete(
    "/:id",

    zValidator(
      "param",
      z.object({
        id: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        if (!id) {
          return c.json(
            {
              error: "Missing id",
            },
            400,
          );
        }

        const transactionsToDelete = db.$with("transactions_to_delete").as(
          db
            .select({ id: transactions.id })
            .from(transactions)
            .innerJoin(accounts, eq(accounts.id, transactions.accountId))
            .where(and(eq(transactions.id, id), eq(accounts.userId, userId))),
        );

        const [data] = await db
          .with(transactionsToDelete)
          .delete(transactions)
          .where(
            inArray(
              transactions.id,
              sql`(select id from ${transactionsToDelete})`,
            ),
          )
          .returning({
            id: transactions.id,
          });

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  );

export default app;
