"use client";
import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useSearchParams } from "next/navigation";
import { useCurrentUserId } from "@/lib/utils";

export const useGetItemTransactions = (overrideParams?: {
  from?: string;
  to?: string;
  accountId?: string;
  itemId?: string;
  categoryId?: string;
}) => {
  const params = useSearchParams();
  const from = overrideParams?.from || params.get("from") || "";
  const to = overrideParams?.to || params.get("to") || "";
  const accountId = overrideParams?.accountId || params.get("accountId") || "";
  const itemId = overrideParams?.itemId || params.get("itemId") || "";
  const categoryId = overrideParams?.categoryId || params.get("categoryId") || "";
  const userId = useCurrentUserId();

  const query = useQuery({
    enabled: !!userId,
    queryKey: ["itemTransactions", { from, to, accountId, itemId, categoryId, userId }],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.itemTransactions.$get(
        {
          query: {
            from,
            to,
            accountId,
            itemId,
            categoryId,
          },
        },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch item transactions");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};